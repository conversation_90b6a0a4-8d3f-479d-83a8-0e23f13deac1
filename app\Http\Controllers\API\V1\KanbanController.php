<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\ApiResponse;
use App\Constants\ResMessages;
use Illuminate\Support\Facades\DB;
use App\Models\Task;
use App\Models\TaskHistory;
use App\Models\KanbanColumn;
use App\Models\User;

class KanbanController extends Controller
{
    public function kanbanViewData($projectId, $userId)
    {
        $roleId = User::where('id', $userId)->pluck('role_id')->first();
        $role = DB::table('roles')->where('id', $roleId)->value('code');

        $project = DB::table('projects')
            ->join('kanban_columns', 'projects.id', '=', 'kanban_columns.project_id')
            ->where('projects.id', $projectId)
            ->whereNull('kanban_columns.deleted_at')
            ->select('projects.project_name', 'kanban_columns.id', 'kanban_columns.column_name')
            ->orderBy('kanban_columns.position')
            ->get();

        $projectWithTasks = $project->map(function ($column) use ($userId, $role) {
            $tasksQuery = DB::table('tasks')
                ->where('status', $column->id)
                ->whereNull('deleted_at')
                ->select('title', 'id');
            if ($role === $this->employeeRoleCode) {
                $tasksQuery->where('user_id', $userId);
            }

            $column->tasks = $tasksQuery->get();
            return $column;
        });

        $kanbanData = $projectWithTasks->map(function ($column) {
            return [
                'id' => 'column-' . $column->id,
                'title' => $column->column_name,
                'item' => $column->tasks->map(function ($task) {
                    return [
                        'id' => $task->id,
                        'title' => $task->title,
                        'comments' => null,
                        'badge-text' => null,
                        'badge' => null,
                        'due-date' => null,
                        'attachments' => null,
                        'assigned' => [],
                        'members' => [],
                    ];
                })->toArray(),
            ];
        })->toArray();

        return response()->json(['data' => $kanbanData]);
    }
    public function kanbanUpdateTask(Request $request)
    {
        $task_id = $request->task_id;
        $target_board_id = $request->target_board_id;
        $source_board_id = $request->source_board_id;

        preg_match('/column-(\d+)/', $target_board_id, $targetBoardIdMatches);
        preg_match('/column-(\d+)/', $source_board_id, $sourceBoardIdMatches);

        $targetBoardId = isset($targetBoardIdMatches[1]) ? (int) $targetBoardIdMatches[1] : 0;
        $sourceBoardId = isset($sourceBoardIdMatches[1]) ? (int) $sourceBoardIdMatches[1] : 0;

        $task = Task::find($task_id);

        if ($task) {
            $oldValue = $task->status;

            $task->status = $targetBoardId;
            $task->save();

            TaskHistory::create([
                'task_id' => $task->id,
                'changed_by' => 1,
                'field_changed' => 'status',
                'old_value' => $oldValue,
                'new_value' => $task->status,
                'change_date' => now(),
                'created_at' => now(),
                'updated_at' => null,
            ]);

            return ApiResponse::success($task, ResMessages::UPDATED_SUCCESS);
        } else {
            return ApiResponse::error(null, ResMessages::NOT_FOUND);
        }
    }
    public function kanbanCreateBoard(Request $request)
    {
        $lastColumn = KanbanColumn::where('project_id', $request->project_id)
            ->orderBy('position', 'desc')
            ->first();

        $newPosition = $lastColumn ? $lastColumn->position + 1 : 1;

        $kanbanColumn = KanbanColumn::create([
            'project_id' => $request->project_id,
            'column_name' => $request->title,
            'position' => $newPosition,
            'created_at' => now(),
            'updated_at' => null
        ]);

        return ApiResponse::success($kanbanColumn, ResMessages::CREATED_SUCCESS);
    }
    public function kanbanDeleteBoard($id)
    {
        $kanbanColumn = KanbanColumn::find($id);

        if ($kanbanColumn) {
            $kanbanColumn->delete();
            return ApiResponse::success($kanbanColumn, ResMessages::DELETED_SUCCESS, $kanbanColumn->project_id);
        } else {
            return ApiResponse::error(null, ResMessages::NOT_FOUND);
        }
    }
    public function kanbanUpdateBoardPosition(Request $request)
    {
        $target_board_id = $request->target_board_id;
        $source_board_id = $request->source_board_id;

        preg_match('/column-(\d+)/', $target_board_id, $targetBoardIdMatches);
        preg_match('/column-(\d+)/', $source_board_id, $sourceBoardIdMatches);

        $targetBoardId = isset($targetBoardIdMatches[1]) ? (int) $targetBoardIdMatches[1] : 0;
        $sourceBoardId = isset($sourceBoardIdMatches[1]) ? (int) $sourceBoardIdMatches[1] : 0;

        // Get the source and target boards
        $sourceBoard = KanbanColumn::find($sourceBoardId);
        $targetBoard = KanbanColumn::find($targetBoardId);

        if (!$sourceBoard || !$targetBoard) {
            return ApiResponse::error(null, ResMessages::NOT_FOUND);
        }

        // Get all boards for the project
        $projectBoards = KanbanColumn::where('project_id', $sourceBoard->project_id)
            ->orderBy('position')
            ->get();

        // Update positions
        $newPosition = $targetBoard->position;
        $oldPosition = $sourceBoard->position;

        // If moving forward in the list
        if ($oldPosition < $newPosition) {
            $projectBoards->where('position', '>', $oldPosition)
                ->where('position', '<=', $newPosition)
                ->each(function ($board) {
                    $board->position--;
                    $board->save();
                });
        }
        // If moving backward in the list
        else {
            $projectBoards->where('position', '>=', $newPosition)
                ->where('position', '<', $oldPosition)
                ->each(function ($board) {
                    $board->position++;
                    $board->save();
                });
        }

        // Update the source board's position
        $sourceBoard->position = $newPosition;
        $sourceBoard->save();

        return ApiResponse::success($sourceBoard, ResMessages::UPDATED_SUCCESS);
    }
    public function viewKanban(Request $request)
    {
        $id = $request->Id;

        $data = KanbanColumn::find($id);
        if ($data) {
            return ApiResponse::success($data, ResMessages::RETRIEVED_SUCCESS);
        } else {
            return ApiResponse::error($data, ResMessages::NOT_FOUND);
        }
    }
    public function kanbanUpdateBoard(Request $request)
    {
        $id = $request->id;
        $data = KanbanColumn::find($id);
        if (!$data) {
            return ApiResponse::error(ResMessages::NOT_FOUND, 404);
        }
        if ($data) {
            $data->column_name = $request->title;
            $data->position = $request->display_order;
            $data->updated_at = now();
            $data->save();
            return ApiResponse::success($data, ResMessages::UPDATED_SUCCESS);
        } else {
            return ApiResponse::error($data, ResMessages::NOT_FOUND);
        }
    }
}
