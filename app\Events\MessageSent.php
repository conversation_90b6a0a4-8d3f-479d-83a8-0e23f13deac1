<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class MessageSent implements ShouldBroadcast
{
    use InteractsWithSockets, SerializesModels;

    public $message;

    public $userIds;

    public function __construct($message, $userIds)
    {
        $this->message = $message;
        $this->userIds = $userIds;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('Notifications.' . $this->userIds);
    }

    public function broadcastAs()
    {
        return 'message.sent';
    }
}
